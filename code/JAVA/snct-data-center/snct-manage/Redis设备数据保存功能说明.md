# Redis设备数据保存功能说明

## 功能概述

本功能实现了将设备最新数据保存到Redis中，支持覆盖更新，确保Redis中始终保存的是设备的最新数据。

## 实现方案

### 1. Redis Key设计

#### 设备最新数据Key
```
device:latest:{sn}:{code}
```
- `sn`: 设备序列号
- `code`: 设备代码
- 存储内容: 设备数据的JSON字符串
- 过期时间: 24小时

#### 设备最新时间戳Key
```
device:time:{sn}:{code}
```
- `sn`: 设备序列号
- `code`: 设备代码
- 存储内容: 设备数据的时间戳（Long类型）
- 过期时间: 24小时

### 2. 核心类说明

#### StoreService.java
- **修改内容**: 在`save2Hbase`方法中添加了`saveLatestDataToRedis`调用
- **新增方法**: `saveLatestDataToRedis` - 保存设备最新数据到Redis

#### DeviceDataService.java
- **功能**: 提供设备数据的Redis查询服务
- **主要方法**:
  - `getLatestDeviceData`: 获取设备最新数据
  - `getLatestDeviceTime`: 获取设备最新时间戳
  - `getLatestDeviceObject`: 获取设备最新数据对象
  - `getAllDeviceDataBySn`: 获取船只所有设备数据
  - `hasDeviceData`: 检查设备数据是否存在
  - `deleteDeviceData`: 删除设备数据

#### DeviceDataController.java
- **功能**: 提供设备数据查询的REST API接口
- **接口列表**:
  - `GET /device/data/latest`: 获取设备最新数据
  - `GET /device/data/ship/{sn}`: 获取船只所有设备数据
  - `GET /device/data/exists`: 检查设备数据是否存在
  - `DELETE /device/data/delete`: 删除设备数据

#### CacheConstants.java
- **新增常量**:
  - `DEVICE_LATEST_DATA_KEY`: 设备最新数据Key前缀
  - `DEVICE_LATEST_TIME_KEY`: 设备最新时间戳Key前缀

### 3. 数据流程

1. **数据接收**: Kafka消费者接收设备数据
2. **数据解析**: 将Kafka消息解析为对应的HBase VO对象
3. **Redis保存**: 调用`saveLatestDataToRedis`方法保存到Redis
4. **HBase保存**: 继续原有的HBase保存流程

### 4. 使用示例

#### 4.1 获取设备最新数据
```java
@Autowired
private DeviceDataService deviceDataService;

// 获取设备最新数据JSON
String dataJson = deviceDataService.getLatestDeviceData("SHIP001", "GPS001");

// 获取设备最新时间戳
Long timestamp = deviceDataService.getLatestDeviceTime("SHIP001", "GPS001");

// 获取设备最新数据对象
GpsHbaseVo gpsData = (GpsHbaseVo) deviceDataService.getLatestDeviceObject("SHIP001", "GPS001", DeviceTypeEnum.GPS.getValue());
```

#### 4.2 REST API调用示例
```bash
# 获取设备最新数据
curl "http://localhost:8080/snct-manage/device/data/latest?sn=SHIP001&code=GPS001"

# 获取船只所有设备数据
curl "http://localhost:8080/snct-manage/device/data/ship/SHIP001"

# 检查设备数据是否存在
curl "http://localhost:8080/snct-manage/device/data/exists?sn=SHIP001&code=GPS001"

# 删除设备数据
curl -X DELETE "http://localhost:8080/snct-manage/device/data/delete?sn=SHIP001&code=GPS001"
```

### 5. 配置说明

#### Redis配置
确保application.yml中Redis配置正确：
```yaml
spring:
  redis:
    host: ***************
    port: 6379
    database: 0
    timeout: 10s
    lettuce:
      pool:
        min-idle: 0
        max-idle: 8
        max-active: 8
        max-wait: -1ms
```

### 6. 监控和维护

#### 6.1 日志监控
- 成功保存日志级别: DEBUG
- 错误日志级别: ERROR
- 关键字段: 设备序列号、设备代码、时间戳

#### 6.2 Redis监控
- 监控Key数量: `device:latest:*` 和 `device:time:*`
- 监控内存使用情况
- 监控过期时间设置

### 7. 注意事项

1. **数据一致性**: Redis数据为缓存数据，主数据仍在HBase中
2. **过期时间**: 设置24小时过期时间，避免Redis内存溢出
3. **异常处理**: Redis保存失败不影响HBase保存流程
4. **性能考虑**: Redis操作为异步，不影响主流程性能

### 8. 扩展功能

#### 8.1 可扩展的功能点
- 支持按设备类型批量查询
- 支持数据变化通知
- 支持数据统计和分析
- 支持数据备份和恢复

#### 8.2 性能优化建议
- 使用Redis Pipeline批量操作
- 考虑使用Redis Cluster提高性能
- 定期清理过期数据
- 监控Redis内存使用情况

## 总结

本功能实现了设备数据到Redis的实时保存，支持覆盖更新，提供了完整的查询接口，满足了实时数据查询的需求。通过合理的Key设计和过期时间设置，确保了系统的稳定性和性能。
